@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
      --background: 0 0% 100%;
      --foreground: 240 10% 3.9%;
      --card: 0 0% 100%;
      --card-foreground: 240 10% 3.9%;
      --popover: 0 0% 100%;
      --popover-foreground: 240 10% 3.9%;
      --primary: 240 5.9% 10%;
      --primary-foreground: 0 0% 98%;
      --secondary: 240 4.8% 95.9%;
      --secondary-foreground: 240 5.9% 10%;
      --muted: 240 4.8% 95.9%;
      --muted-foreground: 240 3.8% 46.1%;
      --accent: 240 4.8% 95.9%;
      --accent-foreground: 240 5.9% 10%;
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 0 0% 98%;
      --border: 240 5.9% 90%;
      --input: 240 5.9% 90%;
      --ring: 240 5.9% 10%;
      --radius: 0.75rem;
      --chart-1: 12 76% 61%;
      --chart-2: 173 58% 39%;
      --chart-3: 197 37% 24%;
      --chart-4: 43 74% 66%;
      --chart-5: 27 87% 67%;
      --sidebar-background: 0 0% 98%;
      --sidebar-foreground: 240 5.3% 26.1%;
      --sidebar-primary: 240 5.9% 10%;
      --sidebar-primary-foreground: 0 0% 98%;
      --sidebar-accent: 240 4.8% 95.9%;
      --sidebar-accent-foreground: 240 5.9% 10%;
      --sidebar-border: 240 5.9% 90%;
      --sidebar-ring: 240 5.9% 10%;
    }
  
    .dark {
      --background: 240 10% 3.9%;
      --foreground: 0 0% 98%;
      --card: 240 10% 3.9%;
      --card-foreground: 0 0% 98%;
      --popover: 240 10% 3.9%;
      --popover-foreground: 0 0% 98%;
      --primary: 0 0% 98%;
      --primary-foreground: 240 5.9% 10%;
      --secondary: 240 3.7% 15.9%;
      --secondary-foreground: 0 0% 98%;
      --muted: 240 3.7% 15.9%;
      --muted-foreground: 240 5% 64.9%;
      --accent: 240 3.7% 15.9%;
      --accent-foreground: 0 0% 98%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 0% 98%;
      --border: 240 3.7% 15.9%;
      --input: 240 3.7% 15.9%;
      --ring: 240 4.9% 83.9%;
      --chart-1: 220 70% 50%;
      --chart-2: 160 60% 45%;
      --chart-3: 30 80% 55%;
      --chart-4: 280 65% 60%;
      --chart-5: 340 75% 55%;
      --sidebar-background: 240 5.9% 10%;
      --sidebar-foreground: 0 0% 98%;
      --sidebar-primary: 0 0% 98%;
      --sidebar-primary-foreground: 240 5.9% 10%;
      --sidebar-accent: 240 3.7% 15.9%;
      --sidebar-accent-foreground: 0 0% 98%;
      --sidebar-border: 240 3.7% 15.9%;
      --sidebar-ring: 240 4.9% 83.9%;
    }
}

@layer base {
    * {
      @apply border-border;
    }
    body {
      @apply bg-background text-foreground;
    }
}

@import "@radix-ui/colors/black-alpha.css";
@import "@radix-ui/colors/mauve.css";
@import "@radix-ui/colors/violet.css";
@import "@radix-ui/colors/gray.css";

.AvatarRoot {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	vertical-align: middle;
	overflow: hidden;
	user-select: none;
	width: 30px;
	height: 30px;
	border-radius: 100%;
	background-color: var(--black-a3);
}

.AvatarImage {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: inherit;
}

.AvatarFallback {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgb(233, 233, 233);
	color: var(--black-11);
	font-size: 15px;
	line-height: 1;
	font-weight: 500;
}
  
.DropdownMenuContent,
.DropdownMenuSubContent {
  margin-right: 5px;
  margin-left: 5px;
	min-width: 220px;
	background-color: white;
	border-radius: 6px;
	padding: 5px;
	box-shadow:
		0px 10px 38px -10px rgba(22, 23, 24, 0.35),
		0px 10px 20px -15px rgba(22, 23, 24, 0.2);
	animation-duration: 400ms;
	animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
	will-change: transform, opacity;
}
.DropdownMenuContent[data-side="top"],
.DropdownMenuSubContent[data-side="top"] {
	animation-name: slideDownAndFade;
}
.DropdownMenuContent[data-side="right"],
.DropdownMenuSubContent[data-side="right"] {
	animation-name: slideLeftAndFade;
}
.DropdownMenuContent[data-side="bottom"],
.DropdownMenuSubContent[data-side="bottom"] {
	animation-name: slideUpAndFade;
}
.DropdownMenuContent[data-side="left"],
.DropdownMenuSubContent[data-side="left"] {
	animation-name: slideRightAndFade;
}

.DropdownMenuItem,
.DropdownMenuCheckboxItem,
.DropdownMenuRadioItem,
.DropdownMenuSubTrigger {
	font-size: 13px;
	line-height: 1;
	color: var(--balck-11);
	border-radius: 3px;
	display: flex;
	align-items: center;
	height: 25px;
	padding: 0 5px;
	position: relative;
	padding-left: 25px;
	user-select: none;
	outline: none;
  cursor: pointer;
}
.DropdownMenuSubTrigger[data-state="open"] {
	background-color: var(--violet-4);
	color: var(--violet-11);
}
.DropdownMenuItem[data-disabled],
.DropdownMenuCheckboxItem[data-disabled],
.DropdownMenuRadioItem[data-disabled],
.DropdownMenuSubTrigger[data-disabled] {
	color: var(--mauve-8);
	pointer-events: none;
}
.DropdownMenuItem[data-highlighted],
.DropdownMenuCheckboxItem[data-highlighted],
.DropdownMenuRadioItem[data-highlighted],
.DropdownMenuSubTrigger[data-highlighted] {
	background-color: var(--gray-8);
	color: var(--violet-1);
}

.DropdownMenuLabel {
	padding-left: 25px;
	font-size: 12px;
	line-height: 25px;
	color: var(--mauve-11);
}

.DropdownMenuSeparator {
	height: 1px;
	background-color: var(--violet-6);
	margin: 5px;
}

.DropdownMenuItemIndicator {
	position: absolute;
	left: 0;
	width: 25px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.DropdownMenuArrow {
	fill: white;
}

.IconButton {
	font-family: inherit;
	border-radius: 100%;
	height: 35px;
	width: 35px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	color: var(--black-11);
	background-color: white;
	box-shadow: 0 2px 10px var(--black-a7);
}
.IconButton:hover {
	background-color: var(--gray-3);
}
.IconButton:focus {
	box-shadow: 0 0 0 2px black;
}

.RightSlot {
	margin-left: auto;
	padding-left: 20px;
	color: var(--mauve-11);
}
[data-highlighted] > .RightSlot {
	color: white;
}
[data-disabled] .RightSlot {
	color: var(--mauve-8);
}

@keyframes slideUpAndFade {
	from {
		opacity: 0;
		transform: translateY(2px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideRightAndFade {
	from {
		opacity: 0;
		transform: translateX(-2px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideDownAndFade {
	from {
		opacity: 0;
		transform: translateY(-2px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideLeftAndFade {
	from {
		opacity: 0;
		transform: translateX(2px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}


/* Popover */


.PopoverContent {
	border-radius: 4px;
	padding: 20px;
	width: 260px;
	background-color: white;
	box-shadow:
		hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
		hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
	animation-duration: 400ms;
	animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
	will-change: transform, opacity;
}
.PopoverContent:focus {
	box-shadow:
		hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
		hsl(206 22% 7% / 20%) 0px 10px 20px -15px,
		0 0 0 2px var(--violet-7);
}
.PopoverContent[data-state="open"][data-side="top"] {
	animation-name: slideDownAndFade;
}
.PopoverContent[data-state="open"][data-side="right"] {
	animation-name: slideLeftAndFade;
}
.PopoverContent[data-state="open"][data-side="bottom"] {
	animation-name: slideUpAndFade;
}
.PopoverContent[data-state="open"][data-side="left"] {
	animation-name: slideRightAndFade;
}

.PopoverArrow {
	fill: white;
}

.PopoverClose {
	font-family: inherit;
	border-radius: 100%;
	height: 25px;
	width: 25px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	color: var(--violet-11);
	position: absolute;
	top: 5px;
	right: 5px;
}
.PopoverClose:hover {
	background-color: var(--violet-4);
}
.PopoverClose:focus {
	box-shadow: 0 0 0 2px var(--violet-7);
}

.IconButton {
	font-family: inherit;
	border-radius: 100%;
	height: 35px;
	width: 35px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	color: var(--violet-11);
	background-color: white;
	box-shadow: 0 2px 10px var(--black-a7);
}
.IconButton:hover {
	background-color: var(--violet-3);
}
.IconButton:focus {
	box-shadow: 0 0 0 2px black;
}

.Fieldset {
	display: flex;
	gap: 20px;
	align-items: center;
}

.Label {
	font-size: 13px;
	color: var(--violet-11);
	width: 75px;
}

.Input {
	width: 100%;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	flex: 1;
	border-radius: 4px;
	padding: 0 10px;
	font-size: 13px;
	line-height: 1;
	color: var(--violet-11);
	box-shadow: 0 0 0 1px var(--violet-7);
	height: 25px;
}
.Input:focus {
	box-shadow: 0 0 0 2px var(--violet-8);
}

.Text {
	margin: 0;
	color: var(--mauve-12);
	font-size: 15px;
	line-height: 19px;
	font-weight: 500;
}

@keyframes slideUpAndFade {
	from {
		opacity: 0;
		transform: translateY(2px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideRightAndFade {
	from {
		opacity: 0;
		transform: translateX(-2px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideDownAndFade {
	from {
		opacity: 0;
		transform: translateY(-2px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideLeftAndFade {
	from {
		opacity: 0;
		transform: translateX(2px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}