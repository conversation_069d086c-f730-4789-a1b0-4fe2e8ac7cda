<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Recipe API Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Update Recipe with Minimal Payload (ID + Ingredients only)</h3>
        <p>This test sends only <code>id</code> and <code>ingredients</code> fields to update a recipe.</p>
        <button onclick="testMinimalUpdate()">Run Test</button>
        <div id="test1-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Update Recipe with Full Payload (Backward Compatibility)</h3>
        <p>This test sends all fields to ensure backward compatibility.</p>
        <button onclick="testFullUpdate()">Run Test</button>
        <div id="test2-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Get Recipes (to verify structure)</h3>
        <p>This test fetches existing recipes to see their structure.</p>
        <button onclick="testGetRecipes()">Run Test</button>
        <div id="test3-result"></div>
    </div>

    <script>
        async function testMinimalUpdate() {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.innerHTML = 'Testing...';

            const testPayload = {
                "id": "test-recipe-id", // You'll need to replace this with a real recipe ID
                "ingredients": [
                    {
                        "name": "Ingredient - 1",
                        "quantity": "300",
                        "unit": "ml",
                        "availableStock": 7
                    },
                    {
                        "name": "Ingredient - 2", 
                        "quantity": "500",
                        "unit": "ml",
                        "availableStock": 7
                    }
                ]
            };

            try {
                const response = await fetch('/api/recipe', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testPayload)
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="result success">✅ Success!<pre>${JSON.stringify(result, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed: ${result.error}<pre>${JSON.stringify(result, null, 2)}</pre></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testFullUpdate() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.innerHTML = 'Testing...';

            const testPayload = {
                "id": "test-recipe-id", // You'll need to replace this with a real recipe ID
                "name": "Test Recipe",
                "category": "bebida",
                "amount": 10,
                "ingredients": [
                    {
                        "name": "Ingredient - 1",
                        "quantity": "300",
                        "unit": "ml",
                        "availableStock": 7
                    }
                ]
            };

            try {
                const response = await fetch('/api/recipe', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testPayload)
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="result success">✅ Success!<pre>${JSON.stringify(result, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed: ${result.error}<pre>${JSON.stringify(result, null, 2)}</pre></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testGetRecipes() {
            const resultDiv = document.getElementById('test3-result');
            resultDiv.innerHTML = 'Testing...';

            try {
                const response = await fetch('/api/recipe', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="result success">✅ Success! Found ${result.length} recipes<pre>${JSON.stringify(result.slice(0, 2), null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed: ${result.error}<pre>${JSON.stringify(result, null, 2)}</pre></div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
