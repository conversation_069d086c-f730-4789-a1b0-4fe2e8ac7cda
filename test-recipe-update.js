// Test script to verify the recipe update API works with minimal request body
const testRecipeUpdate = async () => {
    const testPayload = {
        "id": "0c8298a8-892e-4ff6-bde7-fc003f99e611",
        "ingredients": [
            {
                "name": "Ingredient - 1",
                "quantity": "300",
                "unit": "ml",
                "availableStock": 7
            },
            {
                "name": "Ingredient - 2",
                "quantity": "500",
                "unit": "ml",
                "availableStock": 7
            }
        ]
    };

    try {
        const response = await fetch('http://localhost:3001/api/recipe', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testPayload)
        });

        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Recipe update successful:', result);
        } else {
            console.log('❌ Recipe update failed:', result);
        }
    } catch (error) {
        console.error('❌ Error testing recipe update:', error);
    }
};

// Run the test if this script is executed directly
if (typeof window === 'undefined') {
    // Node.js environment
    const fetch = require('node-fetch');
    testRecipeUpdate();
} else {
    // Browser environment
    console.log('Test function created. Call testRecipeUpdate() to run the test.');
}
